package main

import (
	"fmt"
	"net"
	"strings"

	"github.com/shirou/gopsutil/v4/cpu"
	"github.com/shirou/gopsutil/v4/disk"
	"github.com/shirou/gopsutil/v4/host"
)

// HardwareInfo 存储硬件信息
type HardwareInfo struct {
	CPUID      string
	DiskSerial string
	MACAddress string
	HostName   string
}

// GetHardwareInfo 获取系统硬件信息
func GetHardwareInfo() (*HardwareInfo, error) {
	cpuID, err := getCPUID()
	if err != nil {
		return nil, fmt.Errorf("获取CPU ID失败: %w", err)
	}

	diskSerial, err := getDiskSerial()
	if err != nil {
		return nil, fmt.Errorf("获取磁盘序列号失败: %w", err)
	}

	macAddr, err := getMACAddress()
	if err != nil {
		return nil, fmt.Errorf("获取MAC地址失败: %w", err)
	}

	hostname, err := getHostname()
	if err != nil {
		return nil, fmt.Errorf("获取主机名失败: %w", err)
	}

	return &HardwareInfo{
		CPUID:      cpuID,
		DiskSerial: diskSerial,
		MACAddress: macAddr,
		HostName:   hostname,
	}, nil
}

// getCPUID 获取CPU ID
func getCPUID() (string, error) {
	cpuInfo, err := cpu.Info()
	if err != nil {
		return "", err
	}

	if len(cpuInfo) == 0 {
		return "", fmt.Errorf("未找到CPU信息")
	}

	return cpuInfo[0].PhysicalID, nil
}

// getDiskSerial 获取系统磁盘序列号
func getDiskSerial() (string, error) {
	partitions, err := disk.Partitions(false)
	if err != nil {
		return "", err
	}

	if len(partitions) == 0 {
		return "", fmt.Errorf("未找到磁盘分区")
	}

	// 获取系统磁盘分区（通常是第一个）
	for _, partition := range partitions {
		// 获取分区所在的物理设备信息，尝试提取序列号
		// 注意：在不同系统上可能需要不同的实现方式
		return partition.Device, nil
	}

	return "", fmt.Errorf("未找到系统磁盘序列号")
}

// getMACAddress 获取MAC地址
func getMACAddress() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	var macAddress string
	for _, i := range interfaces {
		// 跳过回环接口和没有MAC地址的接口
		if i.Flags&net.FlagLoopback == 0 && len(i.HardwareAddr) > 0 {
			macAddress = i.HardwareAddr.String()
			if macAddress != "" {
				break
			}
		}
	}

	if macAddress == "" {
		return "", fmt.Errorf("未找到有效的MAC地址")
	}

	return strings.ReplaceAll(macAddress, ":", ""), nil
}

// getHostname 获取主机名
func getHostname() (string, error) {
	hostInfo, err := host.Info()
	if err != nil {
		return "", err
	}

	return hostInfo.Hostname, nil
}

// GetHardwareID 根据硬件信息生成硬件ID字符串
func (hi *HardwareInfo) GetHardwareID() string {
	return fmt.Sprintf("%s|%s|%s|%s",
		hi.CPUID,
		hi.DiskSerial,
		hi.MACAddress,
		hi.HostName)
}
