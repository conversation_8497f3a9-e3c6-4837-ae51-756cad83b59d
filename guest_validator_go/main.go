package main

import (
	"crypto/des"
	"crypto/cipher"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"
	"unsafe"
)

// Windows API常量
const (
	EWX_SHUTDOWN = 0x00000001
	EWX_FORCE    = 0x00000004
)

// DES加密密钥种子（与主程序保持一致）
const desKeySeed = "lsw-des-key-2024"

// Windows API函数
var (
	user32           = syscall.NewLazyDLL("user32.dll")
	procExitWindows  = user32.NewProc("ExitWindowsEx")
)

func main() {
	// 设置日志
	logFile := setupLogging()
	defer logFile.Close()

	log.Println("=== Guest Validator Go版本启动 ===")

	// 验证硬件ID
	if err := validateHardwareID(); err != nil {
		log.Printf("验证失败: %v", err)
		shutdownSystem()
		return
	}

	log.Println("验证成功，系统继续运行")
	cleanupTraces()
}

// setupLogging 设置日志记录
func setupLogging() *os.File {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		homeDir = os.TempDir()
	}
	
	logPath := filepath.Join(homeDir, ".lsw", "validator.log")
	os.MkdirAll(filepath.Dir(logPath), 0755)
	
	logFile, err := os.OpenFile(logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		log.SetOutput(os.Stderr)
		return nil
	}
	
	log.SetOutput(logFile)
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	return logFile
}

// validateHardwareID 验证硬件ID
func validateHardwareID() error {
	log.Println("开始验证硬件ID...")

	// 获取加密的guestVar
	encryptedData, err := getGuestVar()
	if err != nil {
		return fmt.Errorf("获取guestVar失败: %w", err)
	}

	// 解析动态guestVar值
	hardwareID, timestamp, signature, err := parseDynamicGuestVar(encryptedData)
	if err != nil {
		return fmt.Errorf("解析guestVar失败: %w", err)
	}

	log.Printf("解析到硬件ID: %s", hardwareID[:8]+"...")
	log.Printf("时间戳: %d", timestamp)

	// 验证签名
	if !verifySignature(hardwareID, timestamp, signature) {
		return fmt.Errorf("签名验证失败")
	}

	// 验证时间戳（允许5分钟的时间差）
	currentTime := time.Now().Unix()
	if abs(currentTime-timestamp) > 300 { // 5分钟
		return fmt.Errorf("时间戳验证失败，时间差过大: %d秒", abs(currentTime-timestamp))
	}

	// 验证存储的硬件ID
	if err := verifyStoredHardwareID(hardwareID); err != nil {
		return fmt.Errorf("存储的硬件ID验证失败: %w", err)
	}

	return nil
}

// getGuestVar 获取VMware guestVar
func getGuestVar() (string, error) {
	// 查找rpctool.exe
	rpctoolPath, err := findRPCTool()
	if err != nil {
		return "", fmt.Errorf("找不到rpctool.exe: %w", err)
	}

	log.Printf("使用rpctool路径: %s", rpctoolPath)

	// 执行rpctool命令
	cmd := exec.Command(rpctoolPath, "info-get", "guestinfo.hardware_id")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("执行rpctool失败: %w", err)
	}

	guestVar := strings.TrimSpace(string(output))
	if guestVar == "" {
		return "", fmt.Errorf("guestVar为空")
	}

	return guestVar, nil
}

// findRPCTool 查找rpctool.exe
func findRPCTool() (string, error) {
	// 常见的VMware Tools路径
	paths := []string{
		`C:\Program Files\VMware\VMware Tools\rpctool.exe`,
		`C:\Program Files (x86)\VMware\VMware Tools\rpctool.exe`,
		`rpctool.exe`, // 在PATH中查找
	}

	for _, path := range paths {
		if _, err := os.Stat(path); err == nil {
			return path, nil
		}
	}

	return "", fmt.Errorf("未找到rpctool.exe")
}

// verifyStoredHardwareID 验证存储的硬件ID
func verifyStoredHardwareID(hardwareID string) error {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("获取用户主目录失败: %w", err)
	}

	// 硬件ID文件路径
	hwIDFile := filepath.Join(homeDir, ".lsw", "hardware.id")

	// 读取存储的硬件ID
	storedData, err := ioutil.ReadFile(hwIDFile)
	if err != nil {
		if os.IsNotExist(err) {
			// 文件不存在，创建新文件
			log.Println("硬件ID文件不存在，创建新文件")
			return saveHardwareID(hardwareID)
		}
		return fmt.Errorf("读取硬件ID文件失败: %w", err)
	}

	storedID := strings.TrimSpace(string(storedData))
	
	// 比较硬件ID
	if !compareHardwareID(hardwareID, storedID) {
		return fmt.Errorf("硬件ID不匹配")
	}

	log.Println("硬件ID验证成功")
	return nil
}

// saveHardwareID 保存硬件ID到文件
func saveHardwareID(hardwareID string) error {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("获取用户主目录失败: %w", err)
	}

	hwIDFile := filepath.Join(homeDir, ".lsw", "hardware.id")
	
	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(hwIDFile), 0755); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}

	// 写入硬件ID
	if err := ioutil.WriteFile(hwIDFile, []byte(hardwareID), 0600); err != nil {
		return fmt.Errorf("写入硬件ID文件失败: %w", err)
	}

	log.Println("硬件ID已保存")
	return nil
}

// compareHardwareID 比较硬件ID
func compareHardwareID(id1, id2 string) bool {
	// 移除格式化字符并转换为大写
	clean1 := strings.ReplaceAll(strings.ToUpper(id1), "-", "")
	clean2 := strings.ReplaceAll(strings.ToUpper(id2), "-", "")
	return clean1 == clean2
}

// shutdownSystem 关闭系统
func shutdownSystem() {
	log.Println("验证失败，系统将关机")

	// 清理痕迹
	cleanupTraces()

	// 尝试使用Windows API关机
	ret, _, err := procExitWindows.Call(
		uintptr(EWX_SHUTDOWN|EWX_FORCE),
		uintptr(0),
	)

	if ret == 0 {
		log.Printf("Windows API关机失败: %v", err)
		// 备用方案：使用shutdown命令
		cmd := exec.Command("shutdown", "/s", "/t", "0", "/f", "/c", "认证验证失败")
		if err := cmd.Run(); err != nil {
			log.Printf("shutdown命令失败: %v", err)
			// 最后的备用方案
			os.Exit(1)
		}
	}
}

// cleanupTraces 清理验证痕迹
func cleanupTraces() {
	log.Println("清理验证痕迹...")

	// 清理临时文件
	tempFiles := []string{
		filepath.Join(os.TempDir(), "vmware_validation.tmp"),
		filepath.Join(os.TempDir(), "hardware_check.tmp"),
	}

	for _, file := range tempFiles {
		os.Remove(file)
	}

	log.Println("痕迹清理完成")
}

// abs 计算绝对值
func abs(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}

// generateDESKey 生成DES密钥（8字节）
func generateDESKey() []byte {
	hash := sha256.Sum256([]byte(desKeySeed))
	return hash[:8] // DES需要8字节密钥
}

// generateSignature 生成签名
func generateSignature(hardwareID string, timestamp int64) string {
	data := fmt.Sprintf("%s:%d", hardwareID, timestamp)
	hash := sha256.Sum256([]byte(data + "signature-salt"))
	return hex.EncodeToString(hash[:16]) // 取前16字节作为签名
}

// parseDynamicGuestVar 解析动态guestVar值
func parseDynamicGuestVar(encryptedData string) (hardwareID string, timestamp int64, signature string, err error) {
	// DES解密
	decrypted, err := decryptDES(encryptedData)
	if err != nil {
		return "", 0, "", fmt.Errorf("DES解密失败: %w", err)
	}

	// 解析数据：hardwareID|timestamp|signature
	parts := strings.Split(decrypted, "|")
	if len(parts) != 3 {
		return "", 0, "", fmt.Errorf("数据格式错误")
	}

	hardwareID = parts[0]
	timestamp, err = strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		return "", 0, "", fmt.Errorf("时间戳解析失败: %w", err)
	}
	signature = parts[2]

	return hardwareID, timestamp, signature, nil
}

// decryptDES 使用DES解密数据
func decryptDES(ciphertext string) (string, error) {
	key := generateDESKey()

	// 解码base64
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("base64解码失败: %w", err)
	}

	// 创建DES cipher
	block, err := des.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建DES cipher失败: %w", err)
	}

	// 检查数据长度
	if len(data) < block.BlockSize() {
		return "", fmt.Errorf("密文数据太短")
	}

	// 分离IV和密文
	iv := data[:block.BlockSize()]
	cipherData := data[block.BlockSize():]

	// 检查密文长度是否为块大小的倍数
	if len(cipherData)%block.BlockSize() != 0 {
		return "", fmt.Errorf("密文长度不正确")
	}

	// 创建CBC模式
	mode := cipher.NewCBCDecrypter(block, iv)

	// 解密数据
	plaintext := make([]byte, len(cipherData))
	mode.CryptBlocks(plaintext, cipherData)

	// 去除填充
	unpaddedData, err := pkcs5Unpadding(plaintext)
	if err != nil {
		return "", fmt.Errorf("去除填充失败: %w", err)
	}

	return string(unpaddedData), nil
}

// pkcs5Unpadding PKCS5去填充
func pkcs5Unpadding(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("数据为空")
	}

	padding := int(data[len(data)-1])
	if padding > len(data) || padding == 0 {
		return nil, fmt.Errorf("填充数据无效")
	}

	// 验证填充
	for i := len(data) - padding; i < len(data); i++ {
		if data[i] != byte(padding) {
			return nil, fmt.Errorf("填充数据不一致")
		}
	}

	return data[:len(data)-padding], nil
}

// verifySignature 验证签名
func verifySignature(hardwareID string, timestamp int64, signature string) bool {
	expectedSignature := generateSignature(hardwareID, timestamp)
	return expectedSignature == signature
}
